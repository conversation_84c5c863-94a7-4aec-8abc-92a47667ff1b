package com.ext.adfusion.max

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.ext.adfusion.interfaces.BannerAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.example.clean0522.R

private const val TAG = "MaxBannerAd"

class MaxBannerAdapter() : BannerAdAdapter {

    override fun loadAd(adUnitId: String) {

    }

    @Composable
    override fun BannerAdView(adUnitId: String, modifier: Modifier) {
        var isAdLoaded by remember { mutableStateOf(false) }
        var maxAdView by remember { mutableStateOf<MaxAdView?>(null) }
        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        maxAdView?.startAutoRefresh()
                        logAd(TAG, "Banner ad resumed")
                    }

                    Lifecycle.Event.ON_PAUSE -> {
                        maxAdView?.let {
                            it.setExtraParameter("allow_pause_auto_refresh_immediately", "true")
                            it.stopAutoRefresh()
                        }
                        logAd(TAG, "Banner ad paused")
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        maxAdView?.destroy()
                        logAd(TAG, "Banner ad destroyed")
                    }

                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
                maxAdView?.destroy()
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(70.dp)
                .background(color = colorResource(R.color.show_ad_bg))
                .padding(5.dp)
                .then(modifier),
            contentAlignment = Alignment.Center
        ) {
            if (!isAdLoaded) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("ad/banner_loading.json")
                )

                LottieAnimation(
                    composition = composition,
                    iterations = LottieConstants.IterateForever
                )
            }

            AndroidView(
                factory = { context ->
                    // Get or create an ad view
                    val adView = MaxAdView(adUnitId)

                    maxAdView = adView

                    adView.apply {
                        setListener(object : MaxAdViewAdListener {
                            override fun onAdLoaded(ad: MaxAd) {
                                logAd(TAG, "Banner ad loaded successfully")
                                isAdLoaded = true
                            }

                            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                                logAd(TAG, "Banner ad failed to load: ${error.message}")
                                isAdLoaded = false
                            }

                            override fun onAdDisplayed(ad: MaxAd) {}

                            override fun onAdHidden(ad: MaxAd) {}

                            override fun onAdClicked(ad: MaxAd) {}

                            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                                logAd(TAG, "Banner ad failed to display: ${error.message}")
                                isAdLoaded = false
                            }

                            override fun onAdExpanded(ad: MaxAd) {}

                            override fun onAdCollapsed(ad: MaxAd) {}
                        })

                        setRevenueListener { maxAd ->
                            AdEventReporter.postPaidEventByMax(maxAd)
                            logAd(TAG, "Ad revenue generated: ${maxAd.revenue}")
                        }

                        loadAd()
                    }
                },
                update = { adView ->
                    // Nothing to update
                }
            )
        }
    }
}