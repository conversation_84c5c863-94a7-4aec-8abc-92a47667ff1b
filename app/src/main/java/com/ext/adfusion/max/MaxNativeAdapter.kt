package com.ext.adfusion.max

import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.ext.adfusion.interfaces.NativeAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.example.clean0522.R

private const val TAG = "MaxNativeAd"

class MaxNativeAdapter(private val context: Context) : NativeAdAdapter {

    private var nativeAdLoader: MaxNativeAdLoader? = null
    private var nativeAd: MaxAd? = null
    private var isLoading = false
    private var isAdRendered = false
    private var adLoadCallback: ((Boolean) -> Unit)? = null

    override fun loadAd(adUnitId: String) {
        loadAd(adUnitId, null)
    }

    override fun loadAd(adUnitId: String, callback: ((Boolean) -> Unit)?) {
        if (isLoading) {
            return
        }

        isLoading = true
        adLoadCallback = callback
        nativeAdLoader?.setNativeAdListener(null)
        nativeAdLoader?.destroy()
        nativeAdLoader = MaxNativeAdLoader(adUnitId)
        nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
            override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {
                logAd(TAG, "Native ad loaded")
                AdEventReporter.analysisPostEvent("max_native_loaded")
                nativeAd?.let {
                    nativeAdLoader?.destroy(it)
                }
                nativeAd = ad
                isLoading = false
                adLoadCallback?.invoke(true)
            }

            override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                logAd(TAG, "Native ad failed to load: ${error.message} ${error.waterfall} ${error.code}")
                AdEventReporter.analysisPostEvent("max_native_load_fail")
                isLoading = false
                adLoadCallback?.invoke(false)
            }

            override fun onNativeAdClicked(ad: MaxAd) {
                logAd(TAG, "Native ad clicked")
                AdEventReporter.analysisPostEvent("max_native_click")
            }
        })

        nativeAdLoader?.setRevenueListener {maxAd ->
            AdEventReporter.analysisPostEvent("max_native_impress")
            logAd(TAG, "Native ad impress")
            AdEventReporter.postPaidEventByMax(maxAd)
        }

        nativeAdLoader?.loadAd()
        logAd(TAG, "Native ad loading...")
        AdEventReporter.analysisPostEvent("max_native_loading")
    }

    override fun isAdLoaded(): Boolean {
        return nativeAd != null
    }

    override fun renderAd(container: ViewGroup, adUnitId: String) {
        val activity = context as? Activity
        if (activity == null) {
            logAd(TAG, "Context is not an Activity, cannot render ad.")
            return
        }

        val currentNativeAd = nativeAd
        val currentLoader = nativeAdLoader
        if (currentLoader == null || currentNativeAd == null) {
            logAd(TAG, "Ad not loaded, loader/ad is null, attempting to load.")
            if (!isLoading) {
                loadAd(adUnitId)
            }
            return
        }

        container.removeAllViews()

        val binder = MaxNativeAdViewBinder.Builder(R.layout.layout_max_native_ad)
            .setTitleTextViewId(R.id.title_text_view)
            .setBodyTextViewId(R.id.body_text_view)
            .setIconImageViewId(R.id.icon_image_view)
            .setMediaContentViewGroupId(R.id.ad_native_media)
            .setOptionsContentViewGroupId(R.id.ad_options_view)
            .setCallToActionButtonId(R.id.cta_button)
            .build()

        val maxNativeAdView = MaxNativeAdView(binder, activity)

        currentLoader.render(maxNativeAdView, currentNativeAd)

        container.addView(maxNativeAdView)
        isAdRendered = true
    }

    @Composable
    override fun NativeAdView(adUnitId: String, modifier: Modifier,showPadding: Boolean) {
        val isLoaded = remember { mutableStateOf(isAdLoaded()) }
        var showLoading by remember { mutableStateOf(true) }

        val composition by rememberLottieComposition(
            LottieCompositionSpec.Asset("ad/ad_native.json")
        )

        val progress by animateLottieCompositionAsState(
            composition = composition,
            iterations = LottieConstants.IterateForever
        )

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> {
                        destroy()
                        logAd(TAG, "Native ad destroyed")
                    }
                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }


        Box(
            modifier = modifier.fillMaxWidth()
                .heightIn(max = 140.dp)
                .background(
                    color = colorResource(R.color.show_ad_bg),
                    shape = RoundedCornerShape(4.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            // Show Lottie animation while ad is loading
            if (showLoading) {
                LottieAnimation(
                    composition = composition,
                    progress = { progress },
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                )
            }

            // AndroidView for the actual ad
            AndroidView(
                factory = { context ->
                    FrameLayout(context).apply {
                        if (!isAdLoaded()) {
                            loadAd(adUnitId) { success ->
                                isLoaded.value = success
                                if (success) {
                                    showLoading = false
                                    renderAd(this, adUnitId)
                                }
                            }
                        } else {
                            showLoading = false
                            renderAd(this, adUnitId)
                        }
                    }
                },
                update = { container ->
                },
                modifier = modifier.fillMaxWidth()
            )
        }
    }

    override fun destroy() {
        nativeAd?.let {
            nativeAdLoader?.destroy(it)
        }
        nativeAd = null
        nativeAdLoader = null
        logAd(TAG, "max native destroy")
    }
}