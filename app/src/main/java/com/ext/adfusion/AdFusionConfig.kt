package com.ext.adfusion

import com.example.clean0522.data.manager.ComPreferencesManager
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.remoteset.ConfigManager
import kotlin.math.abs

/**
 * Configuration class for ad unit IDs and settings
 */
object AdFusionConfig {
    // AdMob Test Ad Unit IDs
    object AdMob {
        // Test IDs for development
        const val TEST_BANNER = "ca-app-pub-3940256099942544/6300978111"
        const val TEST_INTERSTITIAL = "ca-app-pub-3940256099942544/1033173712"
        const val TEST_REWARDED = "ca-app-pub-3940256099942544/5224354917"
        const val TEST_NATIVE_ADVANCED = "ca-app-pub-3940256099942544/2247696110"
        const val TEST_APP_OPEN = "ca-app-pub-3940256099942544/9257395921"

        // Production IDs - Replace with your real ad unit IDs
        const val BANNER = TEST_BANNER
        const val INTERSTITIAL = TEST_INTERSTITIAL
        const val INTERSTITIAL_TIME = TEST_INTERSTITIAL
        const val REWARDED = TEST_REWARDED
        const val NATIVE = TEST_NATIVE_ADVANCED
        const val APP_OPEN = TEST_APP_OPEN
    }

    // AppLovin/MAX Test Ad Unit IDs
    object MAX {
        // Test IDs or placeholder IDs - Replace with your real ad unit IDs
        const val BANNER = ""//"feeb255bdcd6a42d"
        const val INTERSTITIAL = ""//"2c74c7da96214bd5"
        const val INTERSTITIAL_TIME = ""//"2c74c7da96214bd5"
        const val REWARDED = ""//"b1259b6486f91e75"
        const val NATIVE = ""//"77b9bdda79abb799"
        const val APP_OPEN = ""//"104f1385415d8a80"
    }

    const val INTERSTITIAL_INTERVAL_SEC = 90
    const val INTERSTITIAL_OUT_TIME = 5
    const val APPOPEN_OUT_TIME = 5
    const val REWARDED_OUT_TIME = 5

    const val INTERSTITIAL_RP_TIME = -1

    fun getIntersId(): String {
        return if (ConfigManager.getAdSettings().interstitialRpTime >= 0) {
            if ((System.currentTimeMillis() - ComPreferencesManager.instance.getRegisterTime()) >= ConfigManager.getAdSettings().interstitialRpTime * 60 * 1000) {
                if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxInterstitialTimeKey else ConfigManager.getAdKeys().admobInterstitialTimeKey
            } else {
                if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxInterstitialKey else ConfigManager.getAdKeys().admobInterstitialKey
            }
        } else {
            if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxInterstitialKey else ConfigManager.getAdKeys().admobInterstitialKey
        }
    }

    fun getBannerId(): String {
        return if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxBannerKey else ConfigManager.getAdKeys().admobBannerKey
    }

    fun getAppOpenId(): String {
        return if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxOpenKey else ConfigManager.getAdKeys().admobOpenKey
    }

    fun getRewardedId(): String {
        return if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxRewardKey else ConfigManager.getAdKeys().admobRewardKey
    }

    fun getNativeId(): String {
        return if (ConfigManager.getAdKeys().useMaxSdk) ConfigManager.getAdKeys().maxNativeKey else ConfigManager.getAdKeys().admobNativeKey
    }

    fun allowShowAd(): Boolean {
        val interval = abs(System.currentTimeMillis() - AdFusionPreferences.instance.getAdShowLastTime())
        return interval >= ConfigManager.getAdSettings().interstitialInterval * 1000
    }

    fun openNative(): Boolean{
        return ConfigManager.getAdSettings().enableNative
    }

    fun openBanner(): Boolean{
        return ConfigManager.getAdSettings().enableBanner
    }
}