package com.ext.firbase

import android.annotation.SuppressLint
import android.content.Context
import com.ext.remoteset.AdKeys
import com.ext.remoteset.AdSettings
import com.ext.remoteset.AnalyticsManager
import com.ext.remoteset.utils.RemotePreferencesManager
import com.ext.remoteset.utils.PostLogger.logPost
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.gson.Gson
import kotlin.math.abs

object FBAppUtil {
    private val TAG = javaClass.simpleName
    private const val FB_REMOTE_REFRESH = 60 * 60 * 1000
    private const val KEY_CONTROL_KEY = "re_ad_key"
    private const val KEY_CONTROL_INTERVAL = "re_ad_parms"

    @SuppressLint("StaticFieldLeak")
    private var _FirebaseRemoteConfig: FirebaseRemoteConfig? = null

    private fun getConfigInstance(): FirebaseRemoteConfig?{
        if (_FirebaseRemoteConfig == null){
            _FirebaseRemoteConfig = FirebaseRemoteConfig.getInstance()
        }
        return _FirebaseRemoteConfig
    }

    @Synchronized
    fun sdkFirebaseAppInit(context: Context){
        FirebaseApp.initializeApp(context)
        fetchRemoteConfig()
        if (RemotePreferencesManager.instance.getRemoteInstanceId().isEmpty()){
            FirebaseAnalytics.getInstance(context).appInstanceId.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val appInstanceId = task.result
                    RemotePreferencesManager.instance.setRemoteInstanceId(appInstanceId)
                    logPost(TAG, "AppInstance ID: $appInstanceId")
                } else {
                    logPost(TAG, "Unable to get AppInstance ID ${task.exception}")
                }
            }
        }
        postNewToken()
    }

    @Synchronized
    fun postNewToken(){
        if (RemotePreferencesManager.instance.getRemoteGetToken().isBlank()  ||
            abs(System.currentTimeMillis() - RemotePreferencesManager.instance.getRemoteTokenTime()) > 30 * 24 * 60 * 60 * 1000){
            FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
                if (!task.isSuccessful) {
                    logPost(TAG, "Fetching FCM registration token failed")
                    return@OnCompleteListener
                }
                val token = task.result
                if (!token.isNullOrBlank() && RemotePreferencesManager.instance.getRemoteGetToken() != token){
                    RemotePreferencesManager.instance.setRemoteGetToken(token)
                    RemotePreferencesManager.instance.setRemoteTokenTime(System.currentTimeMillis())
                    AnalyticsManager.updateToken(token)
                }
                UpdateEvent.event("request_token")
            })
        }
    }

    @Synchronized
    fun fetchRemoteConfig() {
        if (abs(System.currentTimeMillis() - RemotePreferencesManager.instance.getRemoteFbFlashTime()) < FB_REMOTE_REFRESH) return
        logPost(TAG, "Firebase remote start receive")
        getConfigInstance()?.fetchAndActivate()?.addOnCompleteListener {
            if (it.isSuccessful){
                if (it.result){
                    logPost(TAG, "Firebase remote data receive success")
                    RemotePreferencesManager.instance.setRemoteFbFlashTime(System.currentTimeMillis())
                }
            }
        }
    }

    fun getIntervalControl(): AdSettings?{
        var data: AdSettings? = null
        try {
            val configIdData = getConfigInstance()?.getString(KEY_CONTROL_INTERVAL)
            if (configIdData != null && configIdData.isNotEmpty()){
                data = Gson().fromJson(configIdData, AdSettings::class.java)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return data
    }

    fun getRemoteAdControl(): AdKeys?{
        var data: AdKeys? = null
        try {
            val configAdData = getConfigInstance()?.getString(KEY_CONTROL_KEY)
            if (configAdData != null && configAdData.isNotEmpty()){
                data = Gson().fromJson(configAdData, AdKeys::class.java)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return data
    }

}