package com.ext.firbase

import com.google.firebase.messaging.FirebaseMessagingService
import com.ext.remoteset.AnalyticsManager
import com.ext.remoteset.ConfigManager
import com.ext.remoteset.utils.PostLogger.logPost
import com.ext.remoteset.utils.RemotePreferencesManager

class FirebaseRemoteManageService : FirebaseMessagingService() {

    override fun onCreate() {
        super.onCreate()
        ConfigManager.fetchConfig()
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        logPost("FirebaseRemoteManageService", "onNewToken: $token")
        if (token.isNotEmpty()) {
            RemotePreferencesManager.instance.setRemoteGetToken(token)
            RemotePreferencesManager.instance.setRemoteTokenTime(System.currentTimeMillis())
        }
        AnalyticsManager.updateToken(token)
    }
}