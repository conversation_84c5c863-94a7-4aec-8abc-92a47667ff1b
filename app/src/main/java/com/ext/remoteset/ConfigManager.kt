package com.ext.remoteset

import com.example.clean0522.CleanApp
import com.example.clean0522.data.manager.ComPreferencesManager
import com.ext.adfusion.AdFusionController
import com.ext.remoteset.utils.PostLogger
import com.ext.remoteset.utils.PostLogger.logPost
import com.ext.remoteset.utils.RemotePreferencesManager
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

object ConfigManager {
    private const val TAG = "ConfigManager"
    private var adKeys: AdKeys? = null
    private var adSettings: AdSettings? = null

    @Synchronized
    fun fetchConfig() {
        GlobalScope.launch(Dispatchers.IO) {
            if (AnalyticsConstants.getUserId().isNotEmpty()) {
                val currentTime = System.currentTimeMillis()
                val interval = calculateFetchInterval(currentTime)
                
                if (abs(currentTime - RemotePreferencesManager.instance.getRemoteConfigTime()) >= interval * 1000) {
                    AnalyticsManager.fetchConfiguration(
                        success = {
                            RemotePreferencesManager.instance.setRemoteConfigTime(System.currentTimeMillis())
                            logPost(TAG, "Config fetch success: $it")
                            PostLogger.trackAnalyticsEvent("config_remote_success")
                            try {
                                if (it != null) {
                                    processConfigData(it)
                                    PostLogger.trackAnalyticsEvent("config_set_success")
                                    logPost(TAG, "Config processed successfully")
                                }
                            } catch (e: Exception) {
                                loadCachedConfig()
                                PostLogger.trackAnalyticsEvent("config_set_fail")
                                logPost(TAG, "Config processing failed")
                            }
                        },
                        fail = {
                            loadCachedConfig()
                            PostLogger.trackAnalyticsEvent("config_remote_fail")
                        })
                } else {
                    loadCachedConfig()
                }
            } else {
                logPost(TAG, "Getting remote config from firebase")
                loadCachedConfig()
            }
        }
    }

    private fun calculateFetchInterval(currentTime: Long): Int {
        return when (abs(currentTime - ComPreferencesManager.instance.getRegisterTime()) / 1000 / 60) {
            in 0..2 -> 3
            in 3..10 -> 6 * 60
            in 11..20 -> 12 * 60
            in 21..60 -> 35 * 60
            in 61..120 -> 42 * 60
            else -> 60 * 60
        }
    }

    fun refreshConfig() {
        val endTime = System.currentTimeMillis() + 2 * 60 * 1000L
        GlobalScope.launch(Dispatchers.IO) {
            fetchConfig()
            while (System.currentTimeMillis() < endTime) {
                delay(4000L)
                if (System.currentTimeMillis() < endTime) {
                    fetchConfig()
                }
            }
        }
    }

    private fun loadCachedConfig() {
        val cacheJson = RemotePreferencesManager.instance.getRemoteConfigParams()
        if (cacheJson.isBlank()) {
            logPost(TAG, "No cached config, using firebase")
            PostLogger.trackAnalyticsEvent("config_find_by_fb")
            // Firebase config loading code commented out in original
            // adKeys = FBAppUtil.getRemoteAdControl()
            // adSettings = FBAppUtil.getIntervalControl()
            
            if (adKeys != null && ComPreferencesManager.instance.isTermsAccepted()) {
                GlobalScope.launch {
                    AdFusionController.initAdSdk(CleanApp.appContext, adKeys!!.useMaxSdk)
                }
            }
        } else {
            try {
                logPost(TAG, "Using cached config")
                PostLogger.trackAnalyticsEvent("config_use_cache")
                processConfigData(cacheJson)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun processConfigData(json: String) {
        val data = try {
            Gson().fromJson(json, ConfigParameters::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
        
        if (data != null) {
            data.adKeys?.apply {
                try {
                    adKeys = Gson().fromJson(this, AdKeys::class.java)
                    logPost(TAG, "Processed adKeys: $adKeys")
                    if (ComPreferencesManager.instance.isTermsAccepted()) {
                        GlobalScope.launch {
                            AdFusionController.initAdSdk(CleanApp.appContext, adKeys!!.useMaxSdk)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            
            data.adSettings?.apply {
                try {
                    adSettings = Gson().fromJson(this, AdSettings::class.java)
                    logPost(TAG, "Processed adSettings: $adSettings")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            
            RemotePreferencesManager.instance.setRemoteConfigParams(json)
        }
    }

    fun getAdKeys(): AdKeys {
        loadCachedConfig()
        logPost(TAG, "getAdKeys: $adKeys")
        return adKeys ?: AdKeys()
    }

    fun getAdSettings(): AdSettings {
        loadCachedConfig()
        logPost(TAG, "getAdSettings: $adSettings")
        return adSettings ?: AdSettings()
    }
} 