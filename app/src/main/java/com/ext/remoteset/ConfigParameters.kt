package com.ext.remoteset

import android.os.Parcelable
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import kotlinx.parcelize.Parcelize

@Parcelize
data class ConfigParameters(
    var adKeys: String? = null,
    var adSettings: String? = null,
    var pushContent: String? = null,
    var pushSettings: String? = null
):Parcelable

@Parcelize
data class AdKeys(
    val useMaxSdk: Boolean = false,
    val maxSdkKey: String = AdFusionController.sdk_key,
    val revenueThreshold: Float = 0.1f,
    val maxInterstitialKey: String = AdFusionConfig.MAX.INTERSTITIAL,
    val maxInterstitialTimeKey: String = AdFusionConfig.MAX.INTERSTITIAL_TIME,
    val maxOpenKey: String = AdFusionConfig.MAX.APP_OPEN,
    val maxNativeKey: String = AdFusionConfig.MAX.NATIVE,
    val maxBannerKey: String = AdFusionConfig.MAX.BANNER,
    val maxRewardKey: String = AdFusionConfig.MAX.REWARDED,
    val admobInterstitialKey: String = AdFusionConfig.AdMob.INTERSTITIAL,
    val admobInterstitialTimeKey: String = AdFusionConfig.AdMob.INTERSTITIAL_TIME,
    val admobOpenKey: String = AdFusionConfig.AdMob.APP_OPEN,
    val admobNativeKey: String = AdFusionConfig.AdMob.NATIVE,
    val admobBannerKey: String = AdFusionConfig.AdMob.BANNER,
    val admobRewardKey: String = AdFusionConfig.AdMob.REWARDED
):Parcelable

@Parcelize
data class AdSettings(
    val interstitialInterval: Int = AdFusionConfig.INTERSTITIAL_INTERVAL_SEC,
    val interstitialRpTime: Int = AdFusionConfig.INTERSTITIAL_RP_TIME,
    val lazyOpenTime: Int = AdFusionConfig.APPOPEN_OUT_TIME,
    val lazyInterstitialTime: Int = AdFusionConfig.INTERSTITIAL_OUT_TIME,
    val lazyRewardTime: Int = AdFusionConfig.REWARDED_OUT_TIME,
    val enableNative: Boolean = true,
    val enableBanner: Boolean = true
):Parcelable 