package com.example.clean0522

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.clean0522.ui.components.CustomCheckbox
import com.example.clean0522.ui.components.FileOperationBar
import com.example.clean0522.ui.components.RemoveButton
import com.example.clean0522.ui.features.utility.*
import com.example.clean0522.ui.navigation.Screen
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.LottieAnimationView
import com.example.clean0522.utils.startCountdown
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.InterstitialAdAdapter

/**
 * Activity for file utility screens
 */
class FileUtilityActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_UTILITY_TYPE = "utility_type"
        const val TYPE_LARGE_FILES = "large_files"
        const val TYPE_RECENT_FILES = "recent_files"
        const val TYPE_DUPLICATE_FILES = "duplicate_files"
        const val TYPE_REDUNDANT_FILES = "redundant_files"
        const val TYPE_SIMILAR_PHOTOS = "similar_photos"
    }

    @Composable
    override fun setHomePage() {
        val utilityType = intent.getStringExtra(EXTRA_UTILITY_TYPE) ?: TYPE_LARGE_FILES
        Clean0522Theme {
            FileUtilityScreen(
                utilityType = utilityType,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * File utility screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FileUtilityScreen(
    utilityType: String,
    onBackClick: () -> Unit
) {
    val navController = rememberNavController()

    val viewModel: FileUtilityViewModel = viewModel()

    val isSelectionMode = viewModel.isSelectionMode

    val supportsSorting = utilityType == FileUtilityActivity.TYPE_LARGE_FILES ||
                         utilityType == FileUtilityActivity.TYPE_RECENT_FILES

    val showSortButton = supportsSorting && !isSelectionMode

    val showSelectAllButton = isSelectionMode && (
        supportsSorting ||
        utilityType == FileUtilityActivity.TYPE_DUPLICATE_FILES ||
        utilityType == FileUtilityActivity.TYPE_REDUNDANT_FILES ||
        utilityType == FileUtilityActivity.TYPE_SIMILAR_PHOTOS
    )

    var showAnimate by remember { mutableStateOf(false) }


    val context = LocalContext.current
    var intersAdInstance: InterstitialAdAdapter? = null
    var adIsLoaded = false
    var adIsShowed = false

    intersAdInstance = AdFusionController.getInterstitialAd(context).apply {
        setAdListener(object : AdEventListener() {
            override fun onAdLoaded() {
                super.onAdLoaded()
                adIsLoaded = true
            }

            override fun onAdDisplay() {
                super.onAdDisplay()
                adIsShowed = true
            }
        })
        loadAd()
    }


    val lifecycleOwner = LocalLifecycleOwner.current

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    intersAdInstance?.onDestroy()
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(Unit) {
        showAnimate = true
        startCountdown(
            durationSeconds = 8,
            onTick = {
                if (adIsLoaded && !adIsShowed && it > 2){
                    intersAdInstance.showAd(context as Activity,
                        onAdDismissed = {
                            showAnimate = false
                        })
                }
            },
            onFinish = { showAnimate = false }
        )
    }

    BackHandler {
        if (isSelectionMode &&
            utilityType != FileUtilityActivity.TYPE_REDUNDANT_FILES
            && utilityType != FileUtilityActivity.TYPE_DUPLICATE_FILES) {
            viewModel.exitSelectionMode()
        } else {
            onBackClick()
        }
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            Column {
                TopNavBar(
                    title = when (utilityType) {
                        FileUtilityActivity.TYPE_LARGE_FILES -> stringResource(R.string.utility_large_files)
                        FileUtilityActivity.TYPE_RECENT_FILES -> stringResource(R.string.utility_recent_files)
                        FileUtilityActivity.TYPE_DUPLICATE_FILES -> stringResource(R.string.utility_duplicate_files)
                        FileUtilityActivity.TYPE_REDUNDANT_FILES -> stringResource(R.string.utility_redundant_files)
                        FileUtilityActivity.TYPE_SIMILAR_PHOTOS -> stringResource(R.string.utility_similar_photos)
                        else -> stringResource(R.string.utility_file)
                    },
                    showBackButton = true,
                    backButtonAction = {
                        if (isSelectionMode &&
                            utilityType != FileUtilityActivity.TYPE_REDUNDANT_FILES
                            && utilityType != FileUtilityActivity.TYPE_DUPLICATE_FILES
                        ) {
                            viewModel.exitSelectionMode()
                        } else {
                            onBackClick()
                        }
                    },
                    settingsButtonContent = {
                        if (!showAnimate){
                            if (showSelectAllButton) {
                                val allFiles = when (utilityType) {
                                    FileUtilityActivity.TYPE_LARGE_FILES -> viewModel.filteredLargeFiles
                                    FileUtilityActivity.TYPE_RECENT_FILES -> viewModel.recentFiles
                                    FileUtilityActivity.TYPE_DUPLICATE_FILES -> {
                                        viewModel.duplicateFileGroups.flatMap { it.files }
                                    }
                                    FileUtilityActivity.TYPE_SIMILAR_PHOTOS -> {
                                        viewModel.similarPhotoGroups.flatMap { it.files }
                                    }
                                    else -> emptyList()
                                }

                                if (allFiles.isNotEmpty()) {
                                    val allSelected = viewModel.selectedFiles.size == allFiles.size
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(
                                            text = stringResource(R.string.tab_all),
                                            modifier = Modifier.padding(end = 4.dp),
                                            colorResource(R.color.text_black),
                                            fontSize = 18.sp,
                                            fontWeight = FontWeight.SemiBold
                                        )
                                        CustomCheckbox(
                                            checked = allSelected,
                                            onCheckedChange = { viewModel.toggleSelectAll(allFiles) },
                                            size = 18.dp
                                        )
                                    }
                                }
                            }

                            if (showSortButton) {
                                Image(painter = painterResource(R.mipmap.ic_sort),
                                    contentDescription = stringResource(R.string.sort),
                                    modifier = Modifier.size(24.dp)
                                        .clickable {
                                            viewModel.showSortDialog()
                                        }
                                )
                            }
                        }
                    }
                )
                if (AdFusionConfig.openBanner() && !showAnimate && !viewModel.isLoading){
                    AdFusionController.getBannerAd().BannerAdView()
                }
            }
        },
        bottomBar = {
            Column {
                if (isSelectionMode || utilityType == FileUtilityActivity.TYPE_REDUNDANT_FILES
                    || utilityType == FileUtilityActivity.TYPE_DUPLICATE_FILES) {
                    FileUtilityBottomBar(
                        viewModel = viewModel,
                        utilityType = utilityType
                    )
                }
                if (!showAnimate && !viewModel.isLoading){
                    AdFusionController.getNativeAd().NativeAdView()
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
        ) {
            FileUtilityNavHost(
                navController = navController,
                utilityType = utilityType,
                viewModel = viewModel
            )

            if (showAnimate || viewModel.isLoading){
                LottieAnimationView(
                    type = utilityType,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

/**
 * Navigation host for file utility screens
 */
@Composable
fun FileUtilityNavHost(
    navController: NavHostController,
    utilityType: String,
    viewModel: FileUtilityViewModel
) {
    val startDestination = when (utilityType) {
        FileUtilityActivity.TYPE_LARGE_FILES -> Screen.LargeFiles.route
        FileUtilityActivity.TYPE_RECENT_FILES -> Screen.RecentFiles.route
        FileUtilityActivity.TYPE_DUPLICATE_FILES -> Screen.DuplicateFiles.route
        FileUtilityActivity.TYPE_REDUNDANT_FILES -> Screen.RedundantFiles.route
        FileUtilityActivity.TYPE_SIMILAR_PHOTOS -> Screen.SimilarPhotos.route
        else -> Screen.LargeFiles.route
    }

    Box(
        modifier = Modifier.fillMaxSize()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ){
        NavHost(
            navController = navController,
            startDestination = startDestination
        ) {
            composable(Screen.LargeFiles.route) {
                LargeFilesContent(viewModel = viewModel)
            }

            composable(Screen.RecentFiles.route) {
                RecentFilesContent(viewModel = viewModel)
            }

            composable(Screen.DuplicateFiles.route) {
                DuplicateFilesContent(viewModel = viewModel)
            }

            composable(Screen.RedundantFiles.route) {
                RedundantFilesContent(viewModel = viewModel)
            }

            composable(Screen.SimilarPhotos.route) {
                SimilarPhotosContent(viewModel = viewModel)
            }
        }


    }
}

/**
 * Bottom bar for file utility operations
 */
@Composable
fun FileUtilityBottomBar(
    viewModel: FileUtilityViewModel,
    utilityType: String
) {
    val context = LocalContext.current

    // For redundant files, always show remove button

        // For other types, show operation bar based on selection
    if (viewModel.selectedFiles.size == 1) {
        FileOperationBar(
            onDetailClick = { viewModel.showFileDetails() },
            onOpenClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    FileUtils.openFile(context, fileItem.file)
                }
            },
            onShareClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    FileUtils.shareFile(context, fileItem.file)
                }
            },
            onRenameClick = { viewModel.showRenameFile() },
            onRemoveClick = { viewModel.showDeleteConfirmation() }
        )
    } else if (viewModel.selectedFiles.size > 1) {
        RemoveButton(
            selectedSize = viewModel.getSelectedFilesSize(),
            onClick = { viewModel.showDeleteConfirmation() }
        )
    }
}
