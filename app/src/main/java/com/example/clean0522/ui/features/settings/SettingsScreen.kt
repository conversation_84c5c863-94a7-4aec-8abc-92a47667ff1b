package com.example.clean0522.ui.features.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat.getString
import com.example.clean0522.ComponentBaseActivity
import com.example.clean0522.R
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.utils.PermissionUtils
import androidx.core.net.toUri
import com.ext.adfusion.AdFusionController

/**
 * Settings screen composable
 */
@Composable
fun SettingsScreen(
    activity: ComponentBaseActivity? = null
) {
    val context = LocalContext.current
    val notificationPreferencesManager = remember { NotificationPreferencesManager.getInstance() }

    var hasNotificationPermission by remember { mutableStateOf(PermissionUtils.hasNotificationPermission(context)) }
    var notificationPreferenceEnabled by remember { mutableStateOf(notificationPreferencesManager.isNotificationEnabled()) }

    LaunchedEffect(Unit) {
        hasNotificationPermission = PermissionUtils.hasNotificationPermission(context)
    }

    val effectiveNotificationEnabled = hasNotificationPermission && notificationPreferenceEnabled

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Notification setting
        Spacer(modifier = Modifier.height(4.dp))
//        SettingsItem(
//            icon = R.mipmap.set_notify,
//            title = stringResource(R.string.notification),
//            showSwitch = true,
//            switchChecked = effectiveNotificationEnabled,
//            onSwitchChanged = { enabled ->
//                handleNotificationToggle(
//                    enabled = enabled,
//                    hasNotificationPermission = hasNotificationPermission,
//                    activity = activity,
//                    onPermissionGranted = { hasNotificationPermission = true },
//                    onPreferenceChanged = { newValue ->
//                        notificationPreferenceEnabled = newValue
//                        notificationPreferencesManager.setNotificationEnabled(newValue)
//                    }
//                )
//            },
//            onClick = {
//                val targetState = !effectiveNotificationEnabled
//                handleNotificationToggle(
//                    enabled = targetState,
//                    hasNotificationPermission = hasNotificationPermission,
//                    activity = activity,
//                    onPermissionGranted = { hasNotificationPermission = true },
//                    onPreferenceChanged = { newValue ->
//                        notificationPreferenceEnabled = newValue
//                        notificationPreferencesManager.setNotificationEnabled(newValue)
//                    }
//                )
//            }
//        )

        // Developer Email
        SettingsItem(
            icon = R.mipmap.set_email,
            title = stringResource(R.string.developer_email),
            showArrow = true,
            onClick = {
                val intent = Intent(Intent.ACTION_SENDTO).apply {
                    data = "mailto:<EMAIL>".toUri()
                    putExtra(Intent.EXTRA_SUBJECT, getString(context,R.string.feedback))
                }
                context.startActivity(Intent.createChooser(intent, getString(context,R.string.feedback)))
            }
        )

        // Terms of Use
        val termUri = stringResource(R.string.terms_of_use_url)
        SettingsItem(
            icon = R.mipmap.set_term,
            title = stringResource(R.string.terms_of_use),
            showArrow = true,
            onClick = {
                openUri(context, termUri)
            }
        )

        // Privacy Policy
        val privacyUri = stringResource(R.string.privacy_policy_url)
        SettingsItem(
            icon = R.mipmap.set_privacy,
            title = stringResource(R.string.privacy_policy),
            showArrow = true,
            onClick = {
                openUri(context, privacyUri)
            }
        )

        AdFusionController.getNativeAd().NativeAdView()

        Spacer(modifier = Modifier.weight(1f))

        Box(modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ){
            Text(text = getString(context, R.string.version) + " " + context.packageManager.getPackageInfo(context.packageName, 0).versionName,
                fontSize = 14.sp, color = colorResource(R.color.text_gray_70))
        }

    }
}

/**
 * Handle notification toggle logic
 */
private fun handleNotificationToggle(
    enabled: Boolean,
    hasNotificationPermission: Boolean,
    activity: ComponentBaseActivity?,
    onPermissionGranted: () -> Unit,
    onPreferenceChanged: (Boolean) -> Unit
) {
    if (!hasNotificationPermission) {
        activity?.checkNotificationPermissionAndExecute {
            onPermissionGranted()
            onPreferenceChanged(enabled)
        }
    } else {
        onPreferenceChanged(enabled)
    }
}

/**
 * Settings item component
 */
@Composable
fun SettingsItem(
    icon: Int,
    title: String,
    showSwitch: Boolean = false,
    switchChecked: Boolean = false,
    onSwitchChanged: (Boolean) -> Unit = {},
    showArrow: Boolean = false,
    onClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(15.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {

            Image(
                painter = painterResource(icon),
                contentDescription = title,
                modifier = Modifier.size(30.dp)
            )

            // Title
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_black),
                modifier = Modifier.weight(1f)
                    .padding(horizontal = 8.dp)
            )

            // Switch or Arrow
            Box(modifier = Modifier.size(40.dp),
                contentAlignment = Alignment.CenterEnd
            ){
                when {
                    showSwitch -> {
                        Switch(
                            checked = switchChecked,
                            onCheckedChange = onSwitchChanged,
                            thumbContent = {
                                Box(
                                    modifier = Modifier
                                        .size(30.dp)
                                        .background(
                                            color = Color.White,
                                            shape = CircleShape
                                        )
                                )
                            },
                            colors = SwitchDefaults.colors(
                                checkedTrackColor = Color(0xFF20C089),
                                uncheckedTrackColor = Color(0xFFCECECE),
                                checkedBorderColor = Color.Transparent,
                                uncheckedBorderColor = Color.Transparent
                            ),
                        )
                    }
                    showArrow -> {
                        Image(
                            painter = painterResource(R.mipmap.ic_arrow),
                            contentDescription = stringResource(R.string.navigate),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}

fun openUri(context: Context, uir: String){
    try {
        val intent = Intent(Intent.ACTION_VIEW, uir.toUri())
        context.startActivity(intent)
    } catch (e: Exception) {
        e.printStackTrace()
    }
}
