package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV
import java.util.Calendar
import java.util.TimeZone

/**
 * Manager for terms and privacy agreement preferences using MMKV
 */
class ComPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_TERMS_ACCEPTED = "terms_accepted"
        private const val KEY_GUIDE_SHOW = "guide_show"
        private const val KEY_REGISTER_TIME = "register_time"
        private const val KEY_LAST_CHECKED_DAY = "last_checked_day"

        @Volatile
        private var INSTANCE: ComPreferencesManager? = null

        val instance: ComPreferencesManager by lazy {
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: ComPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set terms and privacy agreement accepted state
     */
    fun setTermsAccepted(accepted: Boolean) {
        mmkv.putBoolean(KEY_TERMS_ACCEPTED, accepted)
    }
    
    /**
     * Get terms and privacy agreement accepted state
     * Default is false (not accepted)
     */
    fun isTermsAccepted(): Boolean {
        return mmkv.getBoolean(KEY_TERMS_ACCEPTED, false)
    }

    fun setShowGuide(accepted: Boolean) {
        mmkv.putBoolean(KEY_GUIDE_SHOW, accepted)
    }

    fun isShowGuide(): Boolean {
        return mmkv.getBoolean(KEY_GUIDE_SHOW, false)
    }

    fun setRegisterTime(time: Long) {
        mmkv.putLong(KEY_REGISTER_TIME, time)
    }

    fun getRegisterTime(): Long {
        return mmkv.getLong(KEY_REGISTER_TIME, 0)
    }
    
    /**
     * Clear all terms preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_TERMS_ACCEPTED)
    }

    fun isNewDay(): Boolean {
        val cal = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        val currentDayStart = cal.timeInMillis

        val lastDayStart = mmkv.getLong(KEY_LAST_CHECKED_DAY, 0)
        return if (currentDayStart != lastDayStart) {
            mmkv.putLong(KEY_LAST_CHECKED_DAY, currentDayStart)
            true
        } else false
    }
}
