package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV

/**
 * Manager for notification preferences using MMKV
 */
class NotificationPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
        private const val KEY_NOTIFICATION_LAST = "notification_last"
        private const val KEY_NOTIFICATION_COUNT = "notification_count"
        
        @Volatile
        private var INSTANCE: NotificationPreferencesManager? = null
        
        fun getInstance(): NotificationPreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set notification enabled state in MMKV
     * This is the user preference, separate from system permission
     */
    fun setNotificationEnabled(enabled: Boolean) {
        mmkv.putBoolean(KEY_NOTIFICATION_ENABLED, enabled)
    }
    
    /**
     * Get notification enabled state from MMKV
     * Default is true (enabled)
     */
    fun isNotificationEnabled(): Boolean {
        return mmkv.getBoolean(KEY_NOTIFICATION_ENABLED, true)
    }

    fun getLastNotificationTime(): Long {
        return mmkv.getLong(KEY_NOTIFICATION_LAST, 0)
    }

    fun setLastNotificationTime(time: Long) {
        mmkv.putLong(KEY_NOTIFICATION_LAST, time)
    }

    fun getNotificationCount(): Int {
        return mmkv.getInt(KEY_NOTIFICATION_COUNT, 0)
    }

    fun setNotificationCount() {
        var count = getNotificationCount()
        mmkv.putInt(KEY_NOTIFICATION_COUNT, ++ count)
    }
    
    /**
     * Clear all notification preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_NOTIFICATION_ENABLED)
    }
}
