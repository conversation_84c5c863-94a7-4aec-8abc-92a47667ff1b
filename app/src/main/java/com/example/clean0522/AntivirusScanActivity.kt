package com.example.clean0522

import android.content.Context
import android.content.Intent
import android.os.Environment
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.List
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.domain.model.ScanState
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.ui.features.antivirus.AntivirusScanViewModel
import com.example.clean0522.ui.features.antivirus.AntivirusScreen
import com.example.clean0522.ui.features.antivirus.IgnoreListScreen
import com.example.clean0522.ui.features.antivirus.ScanProgressScreen
import com.example.clean0522.ui.features.antivirus.ScanResultScreen
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.LottieAnimationUtils
import com.example.clean0522.utils.LottieAnimationView
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

/**
 * Activity for antivirus scanning
 */
class AntivirusScanActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_SCAN_TYPE = "scan_type"
        const val SCAN_TYPE_QUICK = "quick"
        const val SCAN_TYPE_FOLDER = "folder"
        const val SCAN_TYPE_COMPLETE = "complete"

        fun createIntent(context: Context, scanType: String): Intent {
            return Intent(context, AntivirusScanActivity::class.java).apply {
                putExtra(EXTRA_SCAN_TYPE, scanType)
            }
        }
    }

    @Composable
    override fun setHomePage() {
        val scanType = intent.getStringExtra(EXTRA_SCAN_TYPE) ?: SCAN_TYPE_QUICK

        Clean0522Theme {
            AntivirusScanScreen(
                scanType = scanType,
                onBackClick = { finish() },
                onNavigateToFeature = { featureId ->
                    navigateToFeature(featureId)
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AntivirusScanScreen(
    scanType: String,
    onBackClick: () -> Unit,
    onNavigateToFeature: (String) -> Unit = {}
) {
    val viewModel: AntivirusScanViewModel = viewModel { AntivirusScanViewModel(CleanApp.appContext) }
    val scanResult by viewModel.scanResult.collectAsState()
    val currentScreen by viewModel.currentScreen.collectAsState()
    val ignoredThreats by viewModel.ignoredThreats.collectAsState()
    val showDeleteDialog by viewModel.showDeleteDialog.collectAsState()

    // Check for deleted threats when returning to the screen
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        // Use a lifecycle observer to check when returning from other activities
        (context as? ComponentActivity)?.lifecycle?.addObserver(object : androidx.lifecycle.DefaultLifecycleObserver {
            override fun onResume(owner: androidx.lifecycle.LifecycleOwner) {
                viewModel.checkAndRemoveDeletedThreats()
            }
        })
    }

    val title = when (currentScreen) {
        AntivirusScreen.IGNORE_LIST -> stringResource(R.string.ignore_list)
        AntivirusScreen.SCAN_RESULT -> when (scanType) {
            AntivirusScanActivity.SCAN_TYPE_QUICK -> stringResource(R.string.antivirus_quick_scan)
            AntivirusScanActivity.SCAN_TYPE_FOLDER -> stringResource(R.string.antivirus_folder_scan)
            AntivirusScanActivity.SCAN_TYPE_COMPLETE -> stringResource(R.string.antivirus_complete_scan)
            else -> stringResource(R.string.antivirus_quick_scan)
        }
    }

    // Start scan when screen is first displayed
    LaunchedEffect(scanType) {
        viewModel.startScan(scanType, Environment.getExternalStorageDirectory().absolutePath)
    }

    BackHandler {
        if (currentScreen == AntivirusScreen.IGNORE_LIST){
            viewModel.navigateToScanResult()
        }else{
            onBackClick()
        }
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            Column {
                TopNavBar(
                    title = if (scanResult.scanState == ScanState.COMPLETED && currentScreen == AntivirusScreen.SCAN_RESULT) stringResource(R.string.finished) else title,
                    showBackButton = true,
                    backButtonAction = {
                        if (currentScreen == AntivirusScreen.IGNORE_LIST){
                            viewModel.navigateToScanResult()
                        }else{
                            onBackClick()
                        } },
                    settingsButtonContent = {
                        if (currentScreen == AntivirusScreen.SCAN_RESULT && scanResult.scanState == ScanState.COMPLETED) {
                            Image(painter = painterResource(R.mipmap.ic_sort),
                                contentDescription = stringResource(R.string.ignore_list),
                                modifier = Modifier.size(20.dp)
                                    .clickable {
                                        viewModel.navigateToIgnoreList()
                                    }
                            )
                        } else {
                            Spacer(modifier = Modifier.size(20.dp))
                        }
                    }
                )
                if (currentScreen == AntivirusScreen.IGNORE_LIST){
                    val showBanner = ignoredThreats.isNotEmpty()
                    if (showBanner){
                        AdFusionController.getBannerAd()
                    }
                }
            }
        }, bottomBar = {
            if (currentScreen == AntivirusScreen.SCAN_RESULT && scanResult.scanState == ScanState.COMPLETED && AdFusionConfig.openBanner()) {
                AdFusionController.getBannerAd().BannerAdView()
            }else if (currentScreen == AntivirusScreen.IGNORE_LIST){
                AdFusionController.getNativeAd().NativeAdView()
            }
        }
    ) { innerPadding ->
        when (currentScreen) {
            AntivirusScreen.SCAN_RESULT -> {
                when (scanResult.scanState) {
                    ScanState.IDLE, ScanState.SCANNING -> {
                        LottieAnimationView(
                            type = LottieAnimationUtils.AnimationType.VIRUS.displayName
                        )
                    }
                    ScanState.COMPLETED -> {
                        ScanResultScreen(
                            scanResult = scanResult,
                            onIgnoreThreat = { threat ->
                                viewModel.ignoreThreat(threat)
                            },
                            onDeleteThreat = { threat ->
                                viewModel.deleteThreat(threat)
                            },
                            onNavigateToFeature = onNavigateToFeature,
                            modifier = Modifier.padding(innerPadding)
                        )
                    }
                    ScanState.ERROR, ScanState.CANCELLED -> {
                        ScanProgressScreen(
                            scanResult = scanResult,
                            scanType = scanType,
                            onCancelClick = onBackClick,
                            modifier = Modifier.padding(innerPadding)
                        )
                    }
                }
            }
            AntivirusScreen.IGNORE_LIST -> {
                IgnoreListScreen(
                    ignoredThreats = ignoredThreats,
                    onUnignoreThreat = { threat ->
                        viewModel.unignoreThreat(threat)
                    },
                    modifier = Modifier.padding(innerPadding)
                )
            }
        }

        // Delete confirmation dialog
        showDeleteDialog?.let { threat ->
            DeleteConfirmationDialog(
                threat = threat,
                onConfirm = {
                    viewModel.confirmDeleteFile(threat)
                },
                onDismiss = {
                    viewModel.hideDeleteDialog()
                }
            )
        }
    }
}

/**
 * Dialog for confirming file deletion
 */
@Composable
private fun DeleteConfirmationDialog(
    threat: ThreatInfo,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.delete_file_title),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = stringResource(R.string.delete_file_message),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Text(
                    text = threat.displayName,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text(stringResource(R.string.delete))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )
}