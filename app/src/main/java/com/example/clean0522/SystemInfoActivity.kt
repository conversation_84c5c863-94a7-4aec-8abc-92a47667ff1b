package com.example.clean0522

import android.app.Activity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.core.graphics.drawable.toBitmap
import android.opengl.GLES20
import android.opengl.GLSurfaceView
import android.os.BatteryManager
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.example.clean0522.ui.components.RamUsageChart
import com.example.clean0522.ui.features.antivirus.FeatureRecommendationCard
import com.example.clean0522.ui.features.antivirus.getFeatureTools
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppProcessUtils
import com.example.clean0522.utils.BatteryInfoUtils
import com.example.clean0522.utils.CpuInfoUtils
import com.example.clean0522.utils.DeviceInfoUtils
import com.example.clean0522.utils.LottieAnimationView
import com.example.clean0522.utils.NetworkInfoUtils
import com.example.clean0522.utils.RamUsageUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import com.example.clean0522.utils.startCountdown
import com.example.clean0522.viewmodel.AppProcessViewModel
import com.example.clean0522.viewmodel.BatteryInfoViewModel
import com.example.clean0522.viewmodel.CpuInfoViewModel
import com.example.clean0522.viewmodel.NetworkInfoViewModel
import com.example.clean0522.viewmodel.RamUsageViewModel
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import kotlinx.coroutines.flow.update

/**
 * Activity for system information and monitoring tools
 */
class SystemInfoActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_INFO_TYPE = "info_type"
        const val TYPE_DEVICE_INFO = "device_info"
        const val TYPE_RAM_USAGE = "ram_usage"
        const val TYPE_BATTERY_INFO = "battery_info"
        const val TYPE_CPU_MONITOR = "cpu_monitor"
        const val TYPE_NETWORK = "network"
        const val TYPE_APP_PROCESS = "app_process"
    }

    @Composable
    override fun setHomePage() {
        val infoType = intent.getStringExtra(EXTRA_INFO_TYPE) ?: TYPE_DEVICE_INFO
        Clean0522Theme {
            SystemInfoScreen(
                infoType = infoType,
                onBackClick = { finish() },
                navigateToFeature = { featureId ->
                    navigateToFeature(featureId)
                }
            )
        }
    }
}

private fun showAnimType(type: String): Boolean{
    return type != SystemInfoActivity.TYPE_DEVICE_INFO && type != SystemInfoActivity.TYPE_NETWORK
}

/**
 * System info screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SystemInfoScreen(
    infoType: String,
    onBackClick: () -> Unit,
    navigateToFeature: (String) -> Unit = {}
) {
    val title = when (infoType) {
        SystemInfoActivity.TYPE_DEVICE_INFO -> stringResource(R.string.tool_device_info)
        SystemInfoActivity.TYPE_RAM_USAGE -> stringResource(R.string.tool_ram_usage)
        SystemInfoActivity.TYPE_BATTERY_INFO -> stringResource(R.string.tool_battery_info)
        SystemInfoActivity.TYPE_CPU_MONITOR -> stringResource(R.string.tool_cpu_monitor)
        SystemInfoActivity.TYPE_NETWORK -> stringResource(R.string.tool_network)
        SystemInfoActivity.TYPE_APP_PROCESS -> stringResource(R.string.tool_app_process)
        else -> stringResource(R.string.system_info_title)
    }

    var showAnimate by remember { mutableStateOf(false) }
    val context = LocalContext.current
    var intersAdInstance: InterstitialAdAdapter? = null
    var adIsLoaded = false
    var adIsShowed = false

    if (showAnimType(infoType)){
        intersAdInstance = AdFusionController.getInterstitialAd(context).apply {
            setAdListener(object : AdEventListener() {
                override fun onAdLoaded() {
                    super.onAdLoaded()
                    adIsLoaded = true
                }

                override fun onAdDisplay() {
                    super.onAdDisplay()
                    adIsShowed = true
                }
            })
            loadAd()
        }


        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> {
                        intersAdInstance?.onDestroy()
                    }
                    else -> {}
                }
            }
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }

    LaunchedEffect(Unit) {
        if (showAnimType(infoType)){
            showAnimate = true
            startCountdown(
                durationSeconds = 8,
                onTick = {
                    if (adIsLoaded && !adIsShowed && it > 2){
                        intersAdInstance?.showAd(context as Activity,
                            onAdDismissed = {
                                showAnimate = false
                            })
                    }
                },
                onFinish = { showAnimate = false }
            )
        }
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            Column {
                TopNavBar(
                    title = title,
                    showBackButton = true,
                    backButtonAction = onBackClick,
                    settingsButtonContent = {}
                )
                if (!showAnimate){
                    if (infoType == SystemInfoActivity.TYPE_APP_PROCESS){
                        if (AdFusionConfig.openBanner()){
                            AdFusionController.getBannerAd().BannerAdView()
                        }
                    }else if (infoType == SystemInfoActivity.TYPE_NETWORK){
                        AdFusionController.getBannerAd().BannerAdView()
                    }
                }
            }

        },
        bottomBar ={
            if (!showAnimate){
                if (infoType == SystemInfoActivity.TYPE_APP_PROCESS){
                    AdFusionController.getNativeAd().NativeAdView()
                }else if (infoType != SystemInfoActivity.TYPE_NETWORK){
                    if (AdFusionConfig.openBanner()){
                        AdFusionController.getBannerAd().BannerAdView()
                    }
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            when (infoType) {
                SystemInfoActivity.TYPE_DEVICE_INFO -> DeviceInfoContent()
                SystemInfoActivity.TYPE_RAM_USAGE -> RamUsageContent(navigateToFeature)
                SystemInfoActivity.TYPE_BATTERY_INFO -> BatteryInfoContent(navigateToFeature)
                SystemInfoActivity.TYPE_CPU_MONITOR -> CpuMonitorContent()
                SystemInfoActivity.TYPE_NETWORK -> NetworkContent()
                SystemInfoActivity.TYPE_APP_PROCESS -> AppProcessContent()
                else -> PlaceholderContent(title)
            }

            if (showAnimate){
                LottieAnimationView(
                    type = infoType,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

/**
 * Device info content with real device information
 */
@Composable
fun DeviceInfoContent() {
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        DeviceHeaderCard()

        AdFusionController.getNativeAd().NativeAdView()

        Card(
            colors = CardDefaults.cardColors(containerColor = Color.White),
            modifier = Modifier.fillMaxWidth(), elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            DeviceInfoUtils.getAllDeviceInfoItems(context).forEach { item ->
                DeviceInfoCard(
                    icon = item.icon,
                    title = item.title,
                    value = item.value
                )
            }
        }

        Spacer(modifier = Modifier.height(6.dp))
    }
}

/**
 * Device header card showing device icon and basic info
 */
@Composable
fun DeviceHeaderCard() {

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painter = painterResource(id = R.drawable.bg_device), contentScale = ContentScale.Fit)
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // Device icon placeholder

        Image(
            painter = painterResource(R.mipmap.tool_device),
            contentDescription = stringResource(R.string.device_image_placeholder),
            modifier = Modifier.size(75.dp),
        )

        Spacer(modifier = Modifier.width(16.dp))

        // Device basic info
        Column {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(modifier = Modifier
                    .width(2.dp)
                    .height(16.dp)
                    .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = DeviceInfoUtils.getBrand(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }

            Spacer(modifier = Modifier.height(6.dp))


            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(modifier = Modifier
                    .width(2.dp)
                    .height(16.dp)
                    .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = DeviceInfoUtils.getModel(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }
    }
}

/**
 * RAM usage content with real-time monitoring
 */
@Composable
fun RamUsageContent(
    navigateToFeature: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val viewModel: RamUsageViewModel = viewModel()
    val ramUsageInfo by viewModel.ramUsageInfo.collectAsState()

    // Start monitoring when the composable is first composed
    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    // Stop monitoring when the composable is disposed
    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }
    val featureRecommendations = getFeatureTools().filter { it.id != "ram_usage" }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
    // RAM usage header
        RamUsageHeader(ramUsageInfo)

        AdFusionController.getNativeAd().NativeAdView()

    // RAM usage chart
        val dataPoints = viewModel.chartDataManager.getDataPoints()
        val (minMemoryBytes, maxMemoryBytes) = viewModel.chartDataManager.getMinMaxMemoryBytes()

        RamUsageChart(
            dataPoints = dataPoints,
            minMemoryBytes = minMemoryBytes,
            maxMemoryBytes = maxMemoryBytes,
            currentRamInfo = ramUsageInfo,
            modifier = Modifier.fillMaxWidth()
        )

        Text(text = stringResource(R.string.here_are_some_other_features),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            modifier = Modifier.padding(vertical = 8.dp)
        )

        featureRecommendations.forEach { feature ->
            FeatureRecommendationCard(
                feature = feature,
                onFeatureClick = { navigateToFeature(feature.id) }
            )

        }
    }
}

/**
 * Battery info content with real-time monitoring
 */
@Composable
fun BatteryInfoContent(
    navigateToFeature: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val viewModel: BatteryInfoViewModel = viewModel()
    val batteryInfo by viewModel.batteryInfo.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }
    val featureRecommendations = getFeatureTools().filter { it.id != "battery_info" }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        BatteryInfoHeader(batteryInfo)

        AdFusionController.getNativeAd().NativeAdView()

        if (batteryInfo != null) {
            Card(
                colors = CardDefaults.cardColors(containerColor = Color.White),
                modifier = Modifier.fillMaxWidth(), elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                BatteryInfoUtils.getBatteryInfoItems(context, batteryInfo!!).forEach { item ->
                    DeviceInfoCard(
                        icon = item.icon,
                        title = item.title,
                        value = item.value
                    )
                }
            }
        }

        Text(text = stringResource(R.string.here_are_some_other_features),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            modifier = Modifier.padding(vertical = 8.dp)
        )

        featureRecommendations.forEach { feature ->
            FeatureRecommendationCard(
                feature = feature,
                onFeatureClick = { navigateToFeature(feature.id) }
            )
        }
    }
}

/**
 * CPU monitor content with real-time monitoring
 */
@Composable
fun CpuMonitorContent() {
    val context = LocalContext.current
    val viewModel: CpuInfoViewModel = viewModel()
    val cpuInfo by viewModel.cpuInfo.collectAsState()
    val gpuRenderer by viewModel.gpuRenderer.collectAsState()
    val gpuVendor by viewModel.gpuVendor.collectAsState()
    val gpuVersion by viewModel.gpuVersion.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    val cpuGpuInfoItems = if (cpuInfo != null) {
        remember(cpuInfo, gpuRenderer, gpuVendor, gpuVersion) {
            CpuInfoUtils.getCpuGpuInfoItems(context, cpuInfo!!, viewModel.getGpuInfo())
        }
    } else {
        emptyList()
    }

    Box {
        GLSurfaceViewComposable(viewModel)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            CpuHeaderCard(cpuInfo)

            AdFusionController.getNativeAd().NativeAdView()

            if (cpuInfo != null) {
                    CpuCoresGrid(cpuInfo!!.cores)
            }

            Card(
                colors = CardDefaults.cardColors(containerColor = Color.White),
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                cpuGpuInfoItems.forEach { item ->
                    DeviceInfoCard(
                        icon = item.icon,
                        title = item.title,
                        value = item.value
                    )
                }
            }
            Spacer(modifier = Modifier.height(6.dp))
        }
    }
}

/**
 * Network content with real-time monitoring
 */
@Composable
fun NetworkContent() {
    val context = LocalContext.current
    val viewModel: NetworkInfoViewModel = viewModel()
    val networkInfo by viewModel.networkInfo.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // WiFi Section
        item {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(painter = painterResource(R.mipmap.de_wifi),
                    contentDescription = null,
                    modifier = Modifier.size(30.dp))

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = stringResource(R.string.wifi_section),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }

        if (networkInfo?.wifiInfo?.isConnected == true) {
            val wifiItems = NetworkInfoUtils.getWiFiInfoItems(context, networkInfo!!.wifiInfo)
            if (wifiItems.isNotEmpty()) {
                item {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        wifiItems.forEach { item ->
                            DeviceInfoCard(
                                title = item.first,
                                value = item.second,
                                showIcon = false
                            )
                        }
                    }
                }
            } else {
                item {
                    NetworkDisconnectedCard(
                        message = stringResource(R.string.wifi_not_connected)
                    )
                }
            }
        } else {
            item {
                NetworkDisconnectedCard(
                    message = stringResource(R.string.wifi_not_connected)
                )
            }
        }

        // Mobile Data Section
        item {
            Spacer(modifier = Modifier.height(8.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(painter = painterResource(R.mipmap.de_mobile),
                    contentDescription = null,
                    modifier = Modifier.size(30.dp))

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = stringResource(R.string.mobile_data_section),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }

        if (networkInfo?.mobileDataInfo != null) {
            val mobileDataItems = NetworkInfoUtils.getMobileDataInfoItems(context, networkInfo!!.mobileDataInfo)
            if (mobileDataItems.isNotEmpty()) {
                item {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        mobileDataItems.forEach { item ->
                            DeviceInfoCard(
                                title = item.first,
                                value = item.second,
                                showIcon = false
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(6.dp))
                }
            } else {
                item {
                    NetworkDisconnectedCard(
                        message = stringResource(R.string.mobile_data_not_available)
                    )
                }
            }
        } else {
            item {
                NetworkDisconnectedCard(
                    message = stringResource(R.string.mobile_data_not_available)
                )
            }
        }
    }
}

/**
 * Network section header
 */
@Composable
fun NetworkSectionHeader(
    title: String,
    icon: ImageVector
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Network icon
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(30.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Section title
            Text(
                text = title,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * Network disconnected card
 */
@Composable
fun NetworkDisconnectedCard(message: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = message,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Device info card composable matching the mockup design
 */
@Composable
fun DeviceInfoCard(
    icon: Int = -1,
    title: String,
    value: String,
    showIcon: Boolean = true
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (showIcon){
            Image(painter = painterResource(id = icon),
                contentDescription = null,
                modifier = Modifier.size(30.dp))

            Spacer(modifier = Modifier.width(4.dp))
        }

        Text(
            text = title,
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_gray_70),
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            textAlign = TextAlign.End,
            modifier = Modifier.padding(start = 2.dp)
                .weight(1.3f)
        )
    }
}

/**
 * RAM usage header showing current usage statistics
 */
@Composable
fun RamUsageHeader(ramUsageInfo: RamUsageUtils.RamUsageInfo?) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .paint(painter = painterResource(R.drawable.bg_ram), contentScale = ContentScale.Fit)
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {

            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Image(
                    painter = painterResource(R.mipmap.tool_ram),
                    contentDescription = stringResource(R.string.device_image_placeholder),
                    modifier = Modifier.size(75.dp),
                )

                Spacer(modifier = Modifier.height(4.dp))

                if (ramUsageInfo != null) {
                    Text(
                        text = stringResource(R.string.ram_usage_percentage, ramUsageInfo.usagePercentage.toInt()),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.text_black)
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // RAM usage info
            Column {
                if (ramUsageInfo != null) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(modifier = Modifier
                            .width(2.dp)
                            .height(16.dp)
                            .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        Text(
                            text = ramUsageInfo.usedMemoryFormatted,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = colorResource(R.color.text_black)
                        )
                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = stringResource(R.string.ram_used),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFFA3A3A3)
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(modifier = Modifier
                            .width(2.dp)
                            .height(16.dp)
                            .background(colorResource(R.color.text_black),RoundedCornerShape(2.dp))
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        Text(
                            text = ramUsageInfo.totalMemoryFormatted,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = colorResource(R.color.text_black)
                        )
                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = stringResource(R.string.ram_total),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFFA3A3A3)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    CustomProgress(ramUsageInfo.usagePercentage,
                        height = 20.dp,
                        width = 140.dp,
                        backgroundColor = Color(0xFFD8D8D8),
                        progressColor = Color(0xFF3D8FFF))
                } else {
                    Text(
                        text = stringResource(R.string.loading),
                        fontSize = 16.sp,
                        color = colorResource(R.color.text_black)
                    )
                }
            }
        }
}

/**
 * Feature suggestion card
 */
@Composable
fun FeatureSuggestionCard(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Feature icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Feature title
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )

            // Arrow icon
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Battery info header showing current battery status
 */
@Composable
fun BatteryInfoHeader(batteryInfo: BatteryInfoUtils.BatteryInfo?) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painter = painterResource(R.drawable.bg_battery), contentScale = ContentScale.Fit)
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(
                painter = painterResource(R.mipmap.tool_battery),
                contentDescription = stringResource(R.string.device_image_placeholder),
                modifier = Modifier.size(75.dp),
            )
            Spacer(modifier = Modifier.height(4.dp))

            if (batteryInfo != null) {
                Text(
                    text = stringResource(R.string.battery_percentage, batteryInfo.level),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }


        Spacer(modifier = Modifier.width(24.dp))

        // Battery status info
        Column {
            if (batteryInfo != null) {
                Text(
                    text = stringResource(batteryInfo.getHealthStringRes()),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))

                val statusColor = when(batteryInfo.status){
                    BatteryManager.BATTERY_STATUS_CHARGING -> Color(0xFF8D62FF)
                    BatteryManager.BATTERY_STATUS_DISCHARGING -> Color(0xFF469FFF)
                    else -> Color(0xFFFCC479)
                }

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Box(modifier = Modifier
                        .size(8.dp)
                        .clip(CircleShape)
                        .background(statusColor)
                    )

                    Spacer(modifier = Modifier.width(6.dp))

                    Text(
                        text = stringResource(batteryInfo.getStatusStringRes()),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = statusColor
                    )
                }
            } else {
                Text(
                    text = stringResource(R.string.loading),
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

    }
}


/**
 * CPU header card showing CPU icon and basic info
 */
@Composable
fun CpuHeaderCard(cpuInfo: CpuInfoUtils.CpuInfo?) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painter = painterResource(id = R.drawable.bg_cpu), contentScale = ContentScale.Fit)
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // CPU icon
        Image(
            painter = painterResource(R.mipmap.tool_cpu), // 使用CPU图标
            contentDescription = "CPU Icon",
            modifier = Modifier.size(75.dp),
        )

        Spacer(modifier = Modifier.width(16.dp))

        // CPU basic info
        Column(
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = cpuInfo?.hardware?.takeIf { it.isNotBlank() } ?: "",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_black)
            )

            Text(
                text = "Cores: ${cpuInfo?.cores?.size ?: 8}",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_gray_70)
            )

            Text(
                text = cpuInfo?.let { getFrequencyRange(it.cores) } ?: "",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_gray_70)
            )
        }
    }
}

/**
 * Get frequency range string from CPU cores
 */
private fun getFrequencyRange(cores: List<CpuInfoUtils.CpuCoreInfo>): String {
    if (cores.isEmpty()) return "Frequency: N/A"

    val minFreq = cores.minOfOrNull { it.minFrequency } ?: 0L
    val maxFreq = cores.maxOfOrNull { it.maxFrequency } ?: 0L

    val minFreqMHz = (minFreq / 1000).toInt()
    val maxFreqMHz = (maxFreq / 1000).toInt()

    return "Frequency: ${minFreqMHz}MHz~${maxFreqMHz}MHz"
}


/**
 * CPU cores grid showing individual core frequencies in 4x2 layout
 */
@Composable
fun CpuCoresGrid(cores: List<CpuInfoUtils.CpuCoreInfo>) {
    Card(
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            cores.chunked(4).forEachIndexed { index, rowCores ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    rowCores.forEach { core ->
                        Box(
                            modifier = Modifier.weight(1f)
                        ) {
                            CpuCoreCard(core)
                        }
                    }
                    repeat(4 - rowCores.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

/**
 * Individual CPU core card
 */
@Composable
fun CpuCoreCard(core: CpuInfoUtils.CpuCoreInfo) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Core${core.coreIndex}",
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_gray_70)
        )
        Spacer(modifier = Modifier.height(2.dp))
        Text(
            text = "${core.currentFrequencyMHz}MHz",
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = colorResource(R.color.text_black)
        )
    }
}

/**
 * GLSurfaceView Composable for GPU info retrieval
 */
@Composable
fun GLSurfaceViewComposable(viewModel: CpuInfoViewModel) {
    AndroidView(
        factory = { context ->
            GLSurfaceView(context).apply {
                setEGLContextClientVersion(2)
                setRenderer(GpuInfoRenderer(viewModel))
                renderMode = GLSurfaceView.RENDERMODE_WHEN_DIRTY
            }
        },
        modifier = Modifier.size(1.dp)
    )
}

/**
 * GPU info renderer for GLSurfaceView
 */
class GpuInfoRenderer(private val viewModel: CpuInfoViewModel) : GLSurfaceView.Renderer {
    override fun onDrawFrame(gl: GL10?) {
        // Not needed for info retrieval
    }

    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        try {
            val version = GLES20.glGetString(GLES20.GL_VERSION) ?: "Unknown"
            val renderer = GLES20.glGetString(GLES20.GL_RENDERER) ?: "Unknown"
            val vendor = GLES20.glGetString(GLES20.GL_VENDOR) ?: "Unknown"

            logD("GpuInfo", "GPU Info Retrieved - Renderer: $renderer, Vendor: $vendor, Version: $version")

            viewModel._gpuRenderer.update { renderer }
            viewModel._gpuVersion.update { version }
            viewModel._gpuVendor.update { vendor }
        } catch (e: Exception) {
            logE("Error retrieving GPU info", e)
        }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        // Not needed for info retrieval
    }
}

/**
 * App process content with running apps monitoring
 */
@Composable
fun AppProcessContent() {
    val context = LocalContext.current
    val viewModel: AppProcessViewModel = viewModel()
    val appProcessSummary by viewModel.appProcessSummary.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val stoppedApps by viewModel.stoppedApps.collectAsState()

    // Track lifecycle to check app status when returning from settings
    val lifecycleOwner = LocalLifecycleOwner.current

    LaunchedEffect(Unit) {
        viewModel.startMonitoring(context)
    }

    // Listen for lifecycle changes to check app status when returning
    LaunchedEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                // Check status of all apps that were marked as potentially stopped
                stoppedApps.forEach { packageName ->
                    viewModel.checkAppStatus(context, packageName)
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopMonitoring()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // App process header
        item {
            AppProcessHeader(appProcessSummary)
        }

        // Non-stopped apps label
        if (appProcessSummary?.runningApps?.isNotEmpty() == true) {
            item {
                Text(
                    text = stringResource(R.string.non_stopped_apps),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }

        if (appProcessSummary?.runningApps?.isNotEmpty() == true) {
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ){
                    appProcessSummary!!.runningApps.forEach { app ->
                        AppProcessItem(
                            app = app,
                            isAppStopped = stoppedApps.contains(app.packageName),
                            onStopClick = { viewModel.stopApp(context, app.packageName) }
                        )
                        if (app != appProcessSummary!!.runningApps.last()) {
                            Divider(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                thickness = 0.5.dp
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
        } else if (!isLoading) {
            item {
                EmptyAppProcessState()
            }
        }

        if (isLoading) {
            item {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
}

/**
 * App process header showing running apps count and RAM usage
 */
@Composable
fun AppProcessHeader(appProcessSummary: AppProcessUtils.AppProcessSummary?) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painter = painterResource(R.drawable.bg_battery), contentScale = ContentScale.Fit)
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(
                painter = painterResource(R.mipmap.tool_battery),
                contentDescription = stringResource(R.string.device_image_placeholder),
                modifier = Modifier.size(75.dp),
            )
            Spacer(modifier = Modifier.height(4.dp))

            if (appProcessSummary != null) {
                Text(
                    text = stringResource(R.string.ram_usage_percentage, appProcessSummary.ramUsagePercentage.toInt()),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
            }
        }

            Spacer(modifier = Modifier.width(16.dp))

            // App process info
            Column {
                if (appProcessSummary != null) {
                    Text(
                        text = stringResource(R.string.running_apps_count, appProcessSummary.runningAppsCount),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.text_black)
                    )
                } else {
                    Text(
                        text = stringResource(R.string.loading),
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
}

/**
 * Individual app process item
 */
@Composable
fun AppProcessItem(
    app: AppProcessUtils.RunningAppInfo,
    isAppStopped: Boolean,
    onStopClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // App icon
        if (app.icon != null) {
            Image(
                painter = BitmapPainter(app.icon.toBitmap().asImageBitmap()),
                contentDescription = app.appName,
                modifier = Modifier.size(48.dp)
            )
        } else {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceVariant,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_apk),
                    contentDescription = app.appName,
                    modifier = Modifier.size(48.dp)
                )
            }
        }

        Spacer(modifier = Modifier.width(6.dp))

        // App info
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = app.appName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(R.color.text_black)
            )
            Text(
                text = app.packageName,
                fontSize = 12.sp,
                lineHeight = 12.sp,
                maxLines = 2,
                color = Color(0xFFA3A3A3)
            )
        }

        Spacer(modifier = Modifier.width(6.dp))


        // Stop button
        Button(
            onClick = onStopClick,
            enabled = !isAppStopped,
            modifier = Modifier.height(36.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isAppStopped)
                    colorResource(R.color.text_gray)
                else
                    Color(0xFFFF4F61)
            ),
            shape = RoundedCornerShape(10.dp)
        ) {
            Text(
                text = if (isAppStopped)
                    stringResource(R.string.stopped)
                else
                    stringResource(R.string.stop),
                fontSize = 12.sp
            )
        }
    }
}

/**
 * Empty state for app process list
 */
@Composable
fun EmptyAppProcessState() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Settings,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.no_running_apps),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = stringResource(R.string.no_running_apps_description),
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}