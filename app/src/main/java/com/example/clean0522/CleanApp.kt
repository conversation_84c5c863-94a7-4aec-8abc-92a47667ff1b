package com.example.clean0522

import android.app.Application
import android.content.Context
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.service.BarServiceManager
import com.ext.adfusion.AdFusionController
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * Application class for initialization
 */
class CleanApp : Application() {
    companion object{
        lateinit var appContext: Context
    }
    override fun onCreate() {
        super.onCreate()
        appContext = this
        MMKV.initialize(this)

        BarServiceManager.startService(this)
        if (ComPreferencesManager.instance.getRegisterTime() == 0L){
            ComPreferencesManager.instance.setRegisterTime(System.currentTimeMillis())
        }

        GlobalScope.launch {
            AdFusionController.initAdSdk(appContext, false)
        }
    }
}
