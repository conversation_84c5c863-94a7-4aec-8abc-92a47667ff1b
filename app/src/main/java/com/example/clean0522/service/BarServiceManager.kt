package com.example.clean0522.service

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.content.ContextCompat
import com.example.clean0522.utils.PermissionUtils


/**
 * Manager class for handling the system monitor service
 */
object BarServiceManager {

    /**
     * Start the system monitor service
     */
    fun startService(context: Context) {
        if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !PermissionUtils.hasNotificationPermission(context))
            || BarMonitorService.isServiceRunning
            || isServiceRunning(context, BarMonitorService::class.java.name)) {
            return
        }

        val serviceIntent = Intent(context, BarMonitorService::class.java)

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context, serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
        } catch (e: Exception) {

        }
    }

    fun isServiceRunning(context: Context, serviceClassName: String?): Bo<PERSON>an {
        try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Int.Companion.MAX_VALUE)
            for (runningService in runningServices) {
                if (serviceClassName == runningService.service.className) {
                    return true
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * Stop the system monitor service
     */
    fun stopService(context: Context) {
        if (!BarMonitorService.isServiceRunning) {
            return
        }

        val serviceIntent = Intent(context, BarMonitorService::class.java)
        serviceIntent.action = BarMonitorService.ACTION_STOP_SERVICE
        context.startService(serviceIntent)
    }

    /**
     * Toggle the service state (start if stopped, stop if running)
     */
    fun toggleService(context: Context) {
        if (BarMonitorService.isServiceRunning) {
            stopService(context)
        } else {
            startService(context)
        }
    }
}