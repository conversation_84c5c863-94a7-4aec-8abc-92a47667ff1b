<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/bg_color"
>

    <FrameLayout
            android:id="@+id/ad_options_view"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="5dp"
            android:layout_alignParentEnd="true"/>

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="5dp"
            android:orientation="horizontal">


        <RelativeLayout
                android:id="@+id/ll_media"
                android:layout_weight="3"
                android:layout_width="0dp"
                android:layout_height="wrap_content">


            <FrameLayout
                    android:id="@+id/ad_native_media"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@color/black" />

            <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/ad_native_media"
                    android:layout_marginStart="2dp"
                    android:layout_margin="4dp"
                    android:gravity="center"
                    android:paddingHorizontal="4dp"
                    android:text="AD"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:textStyle="bold" />

        </RelativeLayout>


        <RelativeLayout
                android:id="@+id/ll_content"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="120dp"
                android:layout_marginStart="4dp">

            <ImageView
                    android:id="@+id/icon_image_view"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    tools:src="@mipmap/ic_launcher" />

            <TextView
                    android:id="@+id/title_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/icon_image_view"
                    android:layout_alignBottom="@id/icon_image_view"
                    android:layout_marginStart="2dp"
                    android:layout_toEndOf="@id/icon_image_view"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lines="2"
                    android:paddingHorizontal="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@color/text_black"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    tools:text="AppLovin Test Ad" />

            <TextView
                    android:id="@+id/body_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/cta_button"
                    android:layout_below="@id/icon_image_view"
                    android:ellipsize="end"
                    android:lineSpacingExtra="2sp"
                    android:paddingEnd="2dp"
                    android:lineHeight="13sp"
                    android:textColor="@color/text_black"
                    android:textSize="11sp"
                    tools:text="This is an AppLovin Test AdThis is an AppLovin Test AdThis is an AppLovin Test AdThis is an AppLovin Test This is an AppLovin Test " />

            <Button
                    android:id="@+id/cta_button"
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:layout_marginBottom="4dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerVertical="true"
                    android:layout_marginHorizontal="4dp"
                    android:background="@color/common_google_signin_btn_text_dark_pressed"
                    android:textColor="@color/text_black"
                    android:textSize="12sp"
                    tools:text="Click" />

        </RelativeLayout>

    </LinearLayout>


</RelativeLayout>


